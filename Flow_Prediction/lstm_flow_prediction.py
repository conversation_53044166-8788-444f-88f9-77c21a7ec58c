import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from neuralforecast import NeuralForecast
from neuralforecast.models import LSTM
from neuralforecast.losses.pytorch import MAE, MSE
import warnings
warnings.filterwarnings('ignore')

def load_and_prepare_data(file_path):
    """
    Load and prepare the data for NeuralForecast with enhanced preprocessing
    """
    try:
        # Read the CSV file
        df = pd.read_csv(file_path)
        print(f"Original data shape: {df.shape}")

        # Check for missing values
        print(f"Missing values per column:\n{df.isnull().sum()}")

        # Remove any empty rows
        df = df.dropna()
        print(f"Data shape after removing NaN: {df.shape}")

        # Convert date column to datetime with proper format
        df['date'] = pd.to_datetime(df['date'], format='%Y/%m/%d')

        # Sort by date to ensure chronological order
        df = df.sort_values('date').reset_index(drop=True)

        # Basic data validation
        if df['OT'].min() <= 0:
            print("Warning: Found non-positive flow values. Consider data cleaning.")

        # Rename columns to match NeuralForecast requirements
        df_prepared = df.rename(columns={
            'date': 'ds',  # timestamp column
            'OT': 'y',     # target variable (flow)
            'P': 'rainfall',  # covariate 1 (precipitation)
            'E': 'evaporation'  # covariate 2 (evaporation)
        })

        # Add unique_id column (required by NeuralForecast)
        df_prepared['unique_id'] = 'flow_series'

        # Reorder columns
        df_prepared = df_prepared[['unique_id', 'ds', 'y', 'rainfall', 'evaporation']]

        # Data statistics
        print(f"\nFinal data shape: {df_prepared.shape}")
        print(f"Date range: {df_prepared['ds'].min()} to {df_prepared['ds'].max()}")
        print(f"Total days: {(df_prepared['ds'].max() - df_prepared['ds'].min()).days}")

        print(f"\nTarget variable (flow) statistics:")
        print(df_prepared['y'].describe())

        print(f"\nCovariates statistics:")
        print(f"Rainfall (P): min={df_prepared['rainfall'].min():.3f}, max={df_prepared['rainfall'].max():.3f}, mean={df_prepared['rainfall'].mean():.3f}")
        print(f"Evaporation (E): min={df_prepared['evaporation'].min():.3f}, max={df_prepared['evaporation'].max():.3f}, mean={df_prepared['evaporation'].mean():.3f}")

        return df_prepared

    except Exception as e:
        print(f"Error loading data: {e}")
        raise

def split_data(df, train_ratio=0.8):
    """
    Split data into training and testing sets
    """
    n_train = int(len(df) * train_ratio)
    
    train_df = df[:n_train].copy()
    test_df = df[n_train:].copy()
    
    print(f"Training data: {len(train_df)} samples")
    print(f"Testing data: {len(test_df)} samples")
    
    return train_df, test_df

def create_lstm_model(forecast_horizon=7, input_size=30):
    """
    Create optimized LSTM model with covariates for flow prediction

    Parameters:
    - forecast_horizon: Number of days to predict ahead
    - input_size: Number of historical days to use as input (lookback window)
    """
    model = LSTM(
        h=forecast_horizon,  # forecast horizon
        input_size=input_size,  # lookback window - increased for better pattern recognition
        encoder_n_layers=3,  # increased layers for better feature extraction
        encoder_hidden_size=256,  # increased hidden size for more capacity
        decoder_hidden_size=128,
        encoder_dropout=0.1,  # increased dropout for better regularization
        loss=MSE(),  # Mean Squared Error loss - good for regression
        learning_rate=0.0005,  # slightly lower learning rate for stability
        max_steps=2000,  # increased training steps
        val_check_steps=100,  # validation check frequency
        early_stop_patience_steps=5,  # patience for early stopping
        batch_size=32,  # batch size for training
        random_seed=42  # for reproducibility
    )

    print(f"LSTM Model Configuration:")
    print(f"- Forecast horizon: {forecast_horizon} days")
    print(f"- Input size (lookback): {input_size} days")
    print(f"- Hidden layers: 3")
    print(f"- Hidden size: 256")
    print(f"- Dropout: 0.3")
    print(f"- Learning rate: 0.0005")
    print(f"- Max training steps: 2000")

    return model

def train_and_predict(train_df, test_df, forecast_horizon=7, input_size=30):
    """
    Train LSTM model and make predictions with enhanced error handling
    """
    try:
        # Validate data
        if len(train_df) < input_size:
            raise ValueError(f"Training data ({len(train_df)} samples) is smaller than input_size ({input_size})")

        if len(test_df) < forecast_horizon:
            print(f"Warning: Test data ({len(test_df)} samples) is smaller than forecast horizon ({forecast_horizon})")
            forecast_horizon = len(test_df)

        # Create the model with optimized parameters
        lstm_model = create_lstm_model(forecast_horizon, input_size)

        # Initialize NeuralForecast
        nf = NeuralForecast(
            models=[lstm_model],
            freq='D'  # Daily frequency
        )

        print(f"\nTraining LSTM model on {len(train_df)} samples...")
        print("This may take a few minutes...")

        # Fit the model with validation set for early stopping
        # Use 20% of training data for validation
        val_size = int(0.2 * len(train_df))
        nf.fit(train_df, val_size=val_size)

        print("Training completed successfully!")
        print(f"Making predictions for {forecast_horizon} days...")

        # Prepare prediction data
        cutoff_date = train_df['ds'].max()

        # Create future dates for prediction
        future_dates = pd.date_range(
            start=cutoff_date + pd.Timedelta(days=1),
            periods=forecast_horizon,
            freq='D'
        )

        # Get corresponding test data for covariates
        test_subset = test_df.head(forecast_horizon).copy()

        # Ensure we have enough covariate data
        if len(test_subset) < forecast_horizon:
            print(f"Warning: Only {len(test_subset)} test samples available for {forecast_horizon} predictions")

        # Create future dataframe with covariates
        future_df = pd.DataFrame({
            'unique_id': ['flow_series'] * len(future_dates),
            'ds': future_dates,
            'rainfall': test_subset['rainfall'].values[:len(future_dates)],
            'evaporation': test_subset['evaporation'].values[:len(future_dates)]
        })

        print(f"Future dataframe shape: {future_df.shape}")
        print(f"Covariates range - Rainfall: [{future_df['rainfall'].min():.3f}, {future_df['rainfall'].max():.3f}]")
        print(f"Covariates range - Evaporation: [{future_df['evaporation'].min():.3f}, {future_df['evaporation'].max():.3f}]")

        # Make predictions
        predictions = nf.predict(futr_df=future_df)

        print("Predictions completed successfully!")
        print(f"Predictions shape: {predictions.shape}")

        return predictions, test_subset, nf

    except Exception as e:
        print(f"Error during training/prediction: {e}")
        raise

def evaluate_predictions(predictions, actual_data):
    """
    Evaluate model performance with comprehensive metrics
    """
    try:
        # Extract values
        actual_values = actual_data['y'].values
        predicted_values = predictions['LSTM'].values

        # Ensure same length
        min_len = min(len(actual_values), len(predicted_values))
        actual_values = actual_values[:min_len]
        predicted_values = predicted_values[:min_len]

        print(f"\nEvaluating {min_len} predictions...")

        # Calculate basic metrics
        mae = np.mean(np.abs(actual_values - predicted_values))
        mse = np.mean((actual_values - predicted_values) ** 2)
        rmse = np.sqrt(mse)

        # Nash-Sutcliffe Efficiency
        mean_actual = np.mean(actual_values)
        nse = 1 - (np.sum((actual_values - predicted_values) ** 2) /
                   np.sum((actual_values - mean_actual) ** 2))

        # Correlation coefficient
        correlation = np.corrcoef(actual_values, predicted_values)[0, 1]

        # Additional metrics
        # Mean Absolute Percentage Error
        mape = np.mean(np.abs((actual_values - predicted_values) / actual_values)) * 100

        # R-squared
        ss_res = np.sum((actual_values - predicted_values) ** 2)
        ss_tot = np.sum((actual_values - mean_actual) ** 2)
        r2 = 1 - (ss_res / ss_tot)

        # Bias
        bias = np.mean(predicted_values - actual_values)

        print("\n=== Model Performance Metrics ===")
        print(f"MAE (Mean Absolute Error): {mae:.4f}")
        print(f"MSE (Mean Squared Error): {mse:.4f}")
        print(f"RMSE (Root Mean Squared Error): {rmse:.4f}")
        print(f"MAPE (Mean Absolute Percentage Error): {mape:.2f}%")
        print(f"NSE (Nash-Sutcliffe Efficiency): {nse:.4f}")
        print(f"R² (Coefficient of Determination): {r2:.4f}")
        print(f"Correlation Coefficient: {correlation:.4f}")
        print(f"Bias: {bias:.4f}")

        # Performance interpretation
        print(f"\n=== Performance Interpretation ===")
        if nse > 0.75:
            print("NSE > 0.75: Very good model performance")
        elif nse > 0.65:
            print("NSE > 0.65: Good model performance")
        elif nse > 0.5:
            print("NSE > 0.5: Satisfactory model performance")
        elif nse > 0:
            print("NSE > 0: Unsatisfactory model performance")
        else:
            print("NSE ≤ 0: Unacceptable model performance")

        return {
            'mae': mae,
            'mse': mse,
            'rmse': rmse,
            'mape': mape,
            'nse': nse,
            'r2': r2,
            'correlation': correlation,
            'bias': bias,
            'actual': actual_values,
            'predicted': predicted_values
        }

    except Exception as e:
        print(f"Error during evaluation: {e}")
        raise

def plot_results(results, actual_data):
    """
    Create comprehensive visualization of prediction results
    """
    try:
        # Set up the figure with better styling
        plt.style.use('default')
        fig, axes = plt.subplots(2, 3, figsize=(18, 12))
        fig.suptitle('LSTM Flow Prediction Results', fontsize=16, fontweight='bold')

        # Plot 1: Time series comparison
        ax1 = axes[0, 0]
        dates = actual_data['ds'].values[:len(results['actual'])]
        ax1.plot(dates, results['actual'], 'g-', linewidth=2, label='Actual Flow', alpha=0.8)
        ax1.plot(dates, results['predicted'], 'r-', linewidth=2, label='Predicted Flow', alpha=0.8)
        ax1.set_title('Flow Prediction Time Series', fontweight='bold')
        ax1.set_xlabel('Date')
        ax1.set_ylabel('Flow (OT)')
        ax1.legend()
        ax1.tick_params(axis='x', rotation=45)
        ax1.grid(True, alpha=0.3)

        # Plot 2: Scatter plot
        ax2 = axes[0, 1]
        ax2.scatter(results['actual'], results['predicted'], alpha=0.6, s=30, c='blue')
        min_val = min(results['actual'].min(), results['predicted'].min())
        max_val = max(results['actual'].max(), results['predicted'].max())
        ax2.plot([min_val, max_val], [min_val, max_val], 'r--', linewidth=2, label='Perfect Prediction')
        ax2.set_xlabel('Actual Flow')
        ax2.set_ylabel('Predicted Flow')
        ax2.set_title('Actual vs Predicted Scatter Plot', fontweight='bold')
        ax2.legend()
        ax2.grid(True, alpha=0.3)

        # Plot 3: Residuals over time
        ax3 = axes[0, 2]
        residuals = results['actual'] - results['predicted']
        ax3.plot(dates, residuals, 'b-', linewidth=1, alpha=0.7)
        ax3.axhline(y=0, color='r', linestyle='--', linewidth=2, label='Zero Line')
        ax3.set_title('Residuals Over Time', fontweight='bold')
        ax3.set_xlabel('Date')
        ax3.set_ylabel('Residuals')
        ax3.tick_params(axis='x', rotation=45)
        ax3.legend()
        ax3.grid(True, alpha=0.3)

        # Plot 4: Residuals histogram
        ax4 = axes[1, 0]
        ax4.hist(residuals, bins=20, alpha=0.7, color='skyblue', edgecolor='black')
        ax4.axvline(x=0, color='r', linestyle='--', linewidth=2, label='Zero')
        ax4.set_title('Residuals Distribution', fontweight='bold')
        ax4.set_xlabel('Residuals')
        ax4.set_ylabel('Frequency')
        ax4.legend()
        ax4.grid(True, alpha=0.3)

        # Plot 5: Performance metrics
        ax5 = axes[1, 1]
        metrics = ['MAE', 'RMSE', 'NSE', 'R²', 'Correlation']
        values = [results['mae'], results['rmse'], results['nse'], results['r2'], results['correlation']]
        colors = ['blue', 'orange', 'green', 'purple', 'red']

        bars = ax5.bar(metrics, values, color=colors, alpha=0.7)
        ax5.set_title('Performance Metrics', fontweight='bold')
        ax5.set_ylabel('Value')
        ax5.tick_params(axis='x', rotation=45)

        # Add value labels on bars
        for bar, value in zip(bars, values):
            height = bar.get_height()
            ax5.text(bar.get_x() + bar.get_width()/2, height + 0.01,
                    f'{value:.3f}', ha='center', va='bottom', fontweight='bold')

        # Plot 6: Flow statistics comparison
        ax6 = axes[1, 2]
        stats_actual = [results['actual'].min(), results['actual'].mean(), results['actual'].max()]
        stats_predicted = [results['predicted'].min(), results['predicted'].mean(), results['predicted'].max()]
        x_pos = np.arange(3)
        width = 0.35

        ax6.bar(x_pos - width/2, stats_actual, width, label='Actual', alpha=0.7, color='green')
        ax6.bar(x_pos + width/2, stats_predicted, width, label='Predicted', alpha=0.7, color='red')
        ax6.set_title('Flow Statistics Comparison', fontweight='bold')
        ax6.set_ylabel('Flow Value')
        ax6.set_xticks(x_pos)
        ax6.set_xticklabels(['Min', 'Mean', 'Max'])
        ax6.legend()
        ax6.grid(True, alpha=0.3)

        plt.tight_layout()

        # Save the plot
        output_path = 'Flow_Prediction/lstm_prediction_results.png'
        plt.savefig(output_path, dpi=300, bbox_inches='tight')
        print(f"Visualization saved to: {output_path}")

        # Show the plot
        plt.show()

    except Exception as e:
        print(f"Error creating plots: {e}")
        # Create a simple fallback plot
        plt.figure(figsize=(10, 6))
        dates = actual_data['ds'].values[:len(results['actual'])]
        plt.plot(dates, results['actual'], 'g-', linewidth=2, label='Actual Flow')
        plt.plot(dates, results['predicted'], 'r-', linewidth=2, label='Predicted Flow')
        plt.title('Flow Prediction Results (Fallback)')
        plt.xlabel('Date')
        plt.ylabel('Flow')
        plt.legend()
        plt.xticks(rotation=45)
        plt.grid(True)
        plt.tight_layout()
        plt.savefig('Flow_Prediction/lstm_prediction_results_simple.png', dpi=300, bbox_inches='tight')
        plt.show()

def main():
    """
    Main function to run the optimized LSTM flow prediction
    """
    print("=" * 60)
    print("LSTM Flow Prediction with Rainfall and Evaporation Covariates")
    print("=" * 60)

    try:
        # Configuration parameters
        TRAIN_RATIO = 0.8
        FORECAST_HORIZON = 30
        INPUT_SIZE = 90  # 3 months lookback

        print(f"Configuration:")
        print(f"- Training ratio: {TRAIN_RATIO}")
        print(f"- Forecast horizon: {FORECAST_HORIZON} days")
        print(f"- Input size (lookback): {INPUT_SIZE} days")
        print()

        # Load and prepare data
        print("Step 1: Loading and preparing data...")
        df = load_and_prepare_data('Flow_Prediction/test.csv')

        # Split data
        print(f"\nStep 2: Splitting data (train: {TRAIN_RATIO}, test: {1-TRAIN_RATIO})...")
        train_df, test_df = split_data(df, train_ratio=TRAIN_RATIO)

        # Train model and make predictions
        print(f"\nStep 3: Training LSTM model and making predictions...")
        predictions, test_subset, trained_model = train_and_predict(
            train_df, test_df,
            forecast_horizon=FORECAST_HORIZON,
            input_size=INPUT_SIZE
        )

        # Evaluate predictions
        print(f"\nStep 4: Evaluating model performance...")
        results = evaluate_predictions(predictions, test_subset)

        # Create visualizations
        print(f"\nStep 5: Creating visualizations...")
        plot_results(results, test_subset)

        # Save detailed results
        print(f"\nStep 6: Saving results...")

        # Create comprehensive results dataframe
        results_df = pd.DataFrame({
            'date': test_subset['ds'].values[:len(results['actual'])],
            'actual_flow': results['actual'],
            'predicted_flow': results['predicted'],
            'residuals': results['actual'] - results['predicted'],
            'rainfall': test_subset['rainfall'].values[:len(results['actual'])],
            'evaporation': test_subset['evaporation'].values[:len(results['actual'])]
        })

        # Save results
        results_path = 'Flow_Prediction/lstm_prediction_results.csv'
        results_df.to_csv(results_path, index=False)

        # Save model performance summary
        summary_path = 'Flow_Prediction/model_performance_summary.txt'
        with open(summary_path, 'w') as f:
            f.write("LSTM Flow Prediction Model Performance Summary\n")
            f.write("=" * 50 + "\n\n")
            f.write(f"Model Configuration:\n")
            f.write(f"- Training samples: {len(train_df)}\n")
            f.write(f"- Test samples: {len(test_df)}\n")
            f.write(f"- Forecast horizon: {FORECAST_HORIZON} days\n")
            f.write(f"- Input size: {INPUT_SIZE} days\n")
            f.write(f"- Training ratio: {TRAIN_RATIO}\n\n")

            f.write(f"Performance Metrics:\n")
            f.write(f"- MAE: {results['mae']:.4f}\n")
            f.write(f"- RMSE: {results['rmse']:.4f}\n")
            f.write(f"- MAPE: {results['mape']:.2f}%\n")
            f.write(f"- NSE: {results['nse']:.4f}\n")
            f.write(f"- R²: {results['r2']:.4f}\n")
            f.write(f"- Correlation: {results['correlation']:.4f}\n")
            f.write(f"- Bias: {results['bias']:.4f}\n")

        print(f"\n" + "=" * 60)
        print("LSTM Flow Prediction Completed Successfully!")
        print("=" * 60)
        print(f"Results saved to: {results_path}")
        print(f"Performance summary: {summary_path}")
        print(f"Visualization: Flow_Prediction/lstm_prediction_results.png")
        print()
        print("Key Performance Metrics:")
        print(f"- RMSE: {results['rmse']:.4f}")
        print(f"- NSE: {results['nse']:.4f}")
        print(f"- Correlation: {results['correlation']:.4f}")

        return results, trained_model

    except Exception as e:
        print(f"\nError in main execution: {e}")
        import traceback
        traceback.print_exc()
        raise

if __name__ == "__main__":
    main()
