#!/usr/bin/env python3
"""
Simple script to run LSTM flow prediction
"""

import sys
import os

# Add current directory to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

try:
    from lstm_flow_prediction import main
    
    print("Starting LSTM Flow Prediction...")
    print("=" * 50)
    
    # Run the main prediction function
    main()
    
    print("=" * 50)
    print("LSTM Flow Prediction completed successfully!")
    
except ImportError as e:
    print(f"Import error: {e}")
    print("Please make sure NeuralForecast is installed:")
    print("pip install neuralforecast")
    
except Exception as e:
    print(f"Error during prediction: {e}")
    print("Please check your data file and try again.")
